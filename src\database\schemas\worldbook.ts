/* eslint-disable sort-keys-fix/sort-keys-fix  */
import {
  boolean,
  index,
  pgTable,
  text,
} from 'drizzle-orm/pg-core';
import { createInsertSchema } from 'drizzle-zod';

import { idGenerator } from '@/database/utils/idGenerator';

import { createdAt, updatedAt } from './_helpers';
import { users } from './user';

/**
 * 世界书表
 * 整合现有功能和新功能
 */
export const worldbooks = pgTable(
  'worldbooks',
  {
    id: text('id')
      .$defaultFn(() => idGenerator('worldbooks'))
      .primaryKey(),

    name: text('name').notNull(),
    description: text('description'),
    enabled: boolean('enabled').default(true),

    userId: text('user_id')
      .references(() => users.id, { onDelete: 'cascade' })
      .notNull(),
    clientId: text('client_id'),

    createdAt: createdAt(),
    updatedAt: updatedAt(),
  }
);



// Zod schemas
export const insertWorldbooksSchema = createInsertSchema(worldbooks);

// 类型推导
export type NewWorldbook = typeof worldbooks.$inferInsert;
export type WorldbookItem = typeof worldbooks.$inferSelect;

import { and, asc, count, desc, eq, ilike, inArray, or } from 'drizzle-orm';

import type { LobeChatDatabase } from '@/database/type';
import { worldbookEntries } from '@/database/schemas/worldbookEntry';
import type { WorldbookEntry } from '@/database/schemas/worldbookEntry';
import type {
  CreateWorldbookEntryParams,
  PaginatedResponse,
  SearchParams,
  UpdateWorldbookEntryParams,
} from '@/types/worldbook/entry';

export class WorldbookEntryModel {
  private userId: string;
  private db: LobeChatDatabase;

  constructor(db: LobeChatDatabase, userId: string) {
    this.db = db;
    this.userId = userId;
  }

  // ====== CRUD Operations ======

  create = async (data: CreateWorldbookEntryParams): Promise<WorldbookEntry> => {
    const [result] = await this.db
      .insert(worldbookEntries)
      .values({
        ...data,
        userId: this.userId,
      })
      .returning();

    return result;
  };

  update = async (id: number, data: UpdateWorldbookEntryParams): Promise<WorldbookEntry | null> => {
    const [result] = await this.db
      .update(worldbookEntries)
      .set({ ...data, updatedAt: new Date() })
      .where(and(eq(worldbookEntries.id, id), eq(worldbookEntries.userId, this.userId)))
      .returning();

    return result || null;
  };

  delete = async (id: number): Promise<void> => {
    await this.db
      .delete(worldbookEntries)
      .where(and(eq(worldbookEntries.id, id), eq(worldbookEntries.userId, this.userId)));
  };

  // ====== Query Operations ======

  findById = async (id: number): Promise<WorldbookEntry | null> => {
    const result = await this.db.query.worldbookEntries.findFirst({
      where: and(
        eq(worldbookEntries.id, id),
        eq(worldbookEntries.userId, this.userId)
      ),
    });

    return result || null;
  };

  findByWorldbookId = async (worldbookId: string): Promise<WorldbookEntry[]> => {
    return this.db.query.worldbookEntries.findMany({
      orderBy: [desc(worldbookEntries.orderIndex)],
      where: and(
        eq(worldbookEntries.worldbookId, worldbookId),
        eq(worldbookEntries.userId, this.userId)
      ),
    });
  };

  findByWorldbookIdWithParams = async (
    worldbookId: string,
    params: SearchParams
  ): Promise<PaginatedResponse<WorldbookEntry>> => {
    const {
      page = 1,
      pageSize = 20,
      sortBy = 'orderIndex',
      sortOrder = 'asc'
    } = params;

    const conditions = this.buildSearchConditions(worldbookId, params);
    const orderBy = this.buildOrderBy(sortBy, sortOrder);

    // 查询总数
    const totalResult = await this.db
      .select({ count: count() })
      .from(worldbookEntries)
      .where(and(eq(worldbookEntries.userId, this.userId), conditions));

    const total = totalResult[0]?.count || 0;

    // 查询数据
    const offset = (page - 1) * pageSize;
    const results = await this.db
      .select()
      .from(worldbookEntries)
      .where(and(eq(worldbookEntries.userId, this.userId), conditions))
      .orderBy(orderBy)
      .limit(pageSize)
      .offset(offset);

    return {
      data: results,
      hasMore: offset + pageSize < total,
      page,
      pageSize,
      total,
    };
  };

  // ====== Batch Operations ======

  bulkCreate = async (entries: CreateWorldbookEntryParams[]): Promise<WorldbookEntry[]> => {
    if (entries.length === 0) return [];

    return this.db.transaction(async (trx) => {
      const BATCH_SIZE = 100; // 每批处理100条记录，避免SQL语句过长
      const results: WorldbookEntry[] = [];

      // 分批处理大量数据
      for (let i = 0; i < entries.length; i += BATCH_SIZE) {
        const batch = entries.slice(i, i + BATCH_SIZE);

        const batchResult = await trx
          .insert(worldbookEntries)
          .values(
            batch.map((entry) => ({
              ...entry,
              userId: this.userId,
            }))
          )
          .returning();

        results.push(...batchResult);
      }

      return results;
    });
  };

  bulkDelete = async (ids: number[]): Promise<number> => {
    const result = await this.db
      .delete(worldbookEntries)
      .where(and(
        inArray(worldbookEntries.id, ids),
        eq(worldbookEntries.userId, this.userId)
      ));

    return result.rowCount || 0;
  };

  bulkToggle = async (ids: number[], enabled: boolean): Promise<number> => {
    const result = await this.db
      .update(worldbookEntries)
      .set({ enabled, updatedAt: new Date() })
      .where(and(
        inArray(worldbookEntries.id, ids),
        eq(worldbookEntries.userId, this.userId)
      ));

    return result.rowCount || 0;
  };

  // ====== Private Helper Methods ======

  private buildSearchConditions = (worldbookId: string, params: SearchParams) => {
    const conditions = [eq(worldbookEntries.worldbookId, worldbookId)];

    if (params.query) {
      conditions.push(
        or(
          ilike(worldbookEntries.title, `%${params.query}%`),
          ilike(worldbookEntries.content, `%${params.query}%`)
        )!
      );
    }

    if (params.enabled !== undefined) {
      conditions.push(eq(worldbookEntries.enabled, params.enabled));
    }

    if (params.activationMode) {
      conditions.push(eq(worldbookEntries.activationMode, params.activationMode));
    }

    return and(...conditions)!;
  };

  private buildOrderBy = (sortBy = 'orderIndex', sortOrder = 'asc') => {
    const isAsc = sortOrder === 'asc';
    switch (sortBy) {
      case 'title': {
        return isAsc ? asc(worldbookEntries.title) : desc(worldbookEntries.title);
      }
      case 'orderIndex': {
        return isAsc ? asc(worldbookEntries.orderIndex) : desc(worldbookEntries.orderIndex);
      }
      case 'createdAt': {
        return isAsc ? asc(worldbookEntries.createdAt) : desc(worldbookEntries.createdAt);
      }
      case 'updatedAt': {
        return isAsc ? asc(worldbookEntries.updatedAt) : desc(worldbookEntries.updatedAt);
      }
      default: {
        return desc(worldbookEntries.orderIndex);
      }
    }
  };
}

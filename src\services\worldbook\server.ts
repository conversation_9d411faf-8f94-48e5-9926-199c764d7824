/* eslint-disable @typescript-eslint/no-unused-vars */
import { lambdaClient } from '@/libs/trpc/client';

import { IWorldbookService } from './type';

export class ServerService implements IWorldbookService {
  // ====== Worldbook Operations ======

  getWorldbooks: IWorldbookService['getWorldbooks'] = async () => {
    return lambdaClient.worldbook.getWorldbooks.query();
  };

  getWorldbook: IWorldbookService['getWorldbook'] = async (id) => {
    return lambdaClient.worldbook.getWorldbookById.query({ id });
  };

  createWorldbook: IWorldbookService['createWorldbook'] = async (data) => {
    return lambdaClient.worldbook.createWorldbook.mutate(data);
  };

  updateWorldbook: IWorldbookService['updateWorldbook'] = async (id, data) => {
    return lambdaClient.worldbook.updateWorldbook.mutate({ data, id });
  };

  deleteWorldbook: IWorldbookService['deleteWorldbook'] = async (id) => {
    await lambdaClient.worldbook.deleteWorldbook.mutate({ id });
  };

  importWorldbook: IWorldbookService['importWorldbook'] = async (data) => {
    return lambdaClient.worldbook.importWorldbook.mutate(data);
  };

  // ====== Entry Operations ======

  getEntry: IWorldbookService['getEntry'] = async (id: number) => {
    return lambdaClient.worldbookEntry.getEntryById.query({ id });
  };

  searchEntries: IWorldbookService['searchEntries'] = async (worldbookId, params) => {
    return lambdaClient.worldbookEntry.searchEntries.query({ params: params || {}, worldbookId });
  };

  createEntry: IWorldbookService['createEntry'] = async (worldbookId, data) => {
    return lambdaClient.worldbookEntry.createEntry.mutate({ ...data, worldbookId });
  };

  updateEntry: IWorldbookService['updateEntry'] = async (id: number, data) => {
    return lambdaClient.worldbookEntry.updateEntry.mutate({ data, id });
  };

  deleteEntry: IWorldbookService['deleteEntry'] = async (id: number) => {
    await lambdaClient.worldbookEntry.deleteEntry.mutate({ id });
  };

  bulkDeleteEntries: IWorldbookService['bulkDeleteEntries'] = async (ids: number[]) => {
    return lambdaClient.worldbookEntry.bulkDeleteEntries.mutate({ ids });
  };

  bulkToggleEntries: IWorldbookService['bulkToggleEntries'] = async (ids: number[], enabled) => {
    return lambdaClient.worldbookEntry.bulkToggleEntries.mutate({ enabled, ids });
  };

}

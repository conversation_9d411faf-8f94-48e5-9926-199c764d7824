'use client';

import { Button, Input } from '@lobehub/ui';
import { Checkbox } from 'antd';
import { createStyles } from 'antd-style';
import { Eye, EyeOff, Plus, Search, Trash2 } from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Center } from 'react-layout-kit';
import { parseAsBoolean, parseAsInteger, parseAsString, parseAsStringEnum, useQueryState } from 'nuqs';

import { message } from '@/components/AntdStaticMethods';


import { useWorldbookStore } from '@/store/worldbook';

import AdvancedFilter, { type FilterOptions } from './AdvancedFilter';
// import { useEntryModal } from '../hooks/useEntryModal';
import DeleteConfirmModal from './DeleteConfirmModal';
import EmptyStatus from './EmptyStatus';
import SkeletonList from './SkeletonList';
import SortSelector, { type SortOptions } from './SortSelector';
import EntryItem from '../../components/EntryItem';
import VirtualizedList from '../../components/VirtualizedList';


const useStyles = createStyles(({ css, token }) => ({
  container: css`
    height: 100%;
    display: flex;
    flex-direction: column;
    background: ${token.colorBgLayout};
  `,
  header: css`
    padding: 16px;
    border-bottom: 1px solid ${token.colorBorderSecondary};
    background: ${token.colorBgLayout};
    flex-shrink: 0;
  `,
  searchContainer: css`
    margin-bottom: 12px;
  `,
  searchRow: css`
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 12px;
  `,
  searchInput: css`
    flex: 1;
  `,
  toolbar: css`
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid ${token.colorBorderSecondary};
    background: ${token.colorBgLayout};
  `,
  toolbarLeft: css`
    display: flex;
    align-items: center;
    gap: 12px;
    color: ${token.colorTextSecondary};
    font-size: 14px;
  `,
  toolbarRight: css`
    display: flex;
    align-items: center;
    gap: 8px;
  `,
  listContainer: css`
    flex: 1;
    min-height: 0;
    position: relative;
    background: ${token.colorBgContainer};
  `,
  batchHeader: css`
    padding: 12px 16px;
    border-bottom: 1px solid ${token.colorBorderSecondary};
    background: ${token.colorBgLayout};
    display: flex;
    align-items: center;
    justify-content: space-between;
  `,
  batchInfo: css`
    display: flex;
    align-items: center;
    gap: 12px;
    color: ${token.colorTextSecondary};
    font-size: 14px;
  `,
  batchActions: css`
    display: flex;
    align-items: center;
    gap: 8px;
  `,
}));

interface EntryListProps {
  worldbookId: string;
  onCreateEntry?: (worldbookId: string) => void;
  onEditEntry?: (worldbookId: string, entry: any) => void;
}

const EntryList = memo<EntryListProps>(({ worldbookId, onCreateEntry, onEditEntry }) => {
  const { t } = useTranslation('worldbook');
  const { styles } = useStyles();

  // 搜索状态管理
  const [inputValue, setInputValue] = useState('');
  const [searchTerm, setSearchTerm] = useQueryState(
    'search',
    parseAsString.withDefault('').withOptions({
      history: 'replace'
    })
  );

  // 初始化输入框值
  useEffect(() => {
    setInputValue(searchTerm);
  }, [searchTerm]);

  // 处理输入变化（只更新输入框，不触发搜索）
  const handleInputChange = useCallback((e: any) => {
    const value = e.target.value;
    setInputValue(value);
  }, []);

  // 手动搜索（点击搜索按钮或按回车）
  const handleSearch = useCallback((e?: any) => {
    e?.stopPropagation?.();
    e?.preventDefault?.();
    setSearchTerm(inputValue);
  }, [inputValue, setSearchTerm]);

  // 处理回车键
  const handleKeyPress = useCallback((e: any) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  }, [handleSearch]);
  const [selectedEntries, setSelectedEntries] = useState<string[]>([]);
  // 使用 URL 参数管理排序状态
  const [sortBy, setSortBy] = useQueryState(
    'sortBy',
    parseAsStringEnum(['name', 'date', 'order', 'probability']).withDefault('order').withOptions({
      history: 'replace'
    })
  );
  const [sortOrder, setSortOrder] = useQueryState(
    'sortOrder',
    parseAsStringEnum(['asc', 'desc']).withDefault('asc').withOptions({
      history: 'replace'
    })
  );

  const sortOptions: SortOptions = { sortBy, sortOrder };
  const setSortOptions = (options: SortOptions) => {
    setSortBy(options.sortBy);
    setSortOrder(options.sortOrder);
  };
  // 使用 URL 参数管理过滤状态
  const [filterEnabled, setFilterEnabled] = useQueryState(
    'enabled',
    parseAsBoolean.withOptions({ history: 'replace' })
  );

  const [filterHasKeys, setFilterHasKeys] = useQueryState(
    'hasKeys',
    parseAsBoolean.withOptions({ history: 'replace' })
  );
  const [filterHasSecondaryKeys, setFilterHasSecondaryKeys] = useQueryState(
    'hasSecondaryKeys',
    parseAsBoolean.withOptions({ history: 'replace' })
  );
  const [filterGroupName, setFilterGroupName] = useQueryState(
    'groupName',
    parseAsString.withOptions({ history: 'replace' })
  );
  const [filterSelectiveLogic, setFilterSelectiveLogic] = useQueryState(
    'selectiveLogic',
    parseAsStringEnum(['AND_ANY', 'NOT_ALL', 'NOT_ANY', 'AND_ALL']).withOptions({ history: 'replace' })
  );
  const [filterOrderMin, setFilterOrderMin] = useQueryState(
    'orderMin',
    parseAsInteger.withOptions({ history: 'replace' })
  );
  const [filterOrderMax, setFilterOrderMax] = useQueryState(
    'orderMax',
    parseAsInteger.withOptions({ history: 'replace' })
  );
  const [filterProbabilityMin, setFilterProbabilityMin] = useQueryState(
    'probabilityMin',
    parseAsInteger.withOptions({ history: 'replace' })
  );
  const [filterProbabilityMax, setFilterProbabilityMax] = useQueryState(
    'probabilityMax',
    parseAsInteger.withOptions({ history: 'replace' })
  );

  // 组合过滤选项
  const filterOptions: FilterOptions = {
    enabled: filterEnabled,
    hasKeys: filterHasKeys,
    hasSecondaryKeys: filterHasSecondaryKeys,
    groupName: filterGroupName || undefined,
    selectiveLogic: filterSelectiveLogic as any,
    orderRange: filterOrderMin !== null && filterOrderMax !== null ? [filterOrderMin, filterOrderMax] : undefined,
    probabilityRange: filterProbabilityMin !== null && filterProbabilityMax !== null ? [filterProbabilityMin, filterProbabilityMax] : undefined,
  };

  const setFilterOptions = (options: FilterOptions) => {
    setFilterEnabled(options.enabled ?? null);
    setFilterHasKeys(options.hasKeys ?? null);
    setFilterHasSecondaryKeys(options.hasSecondaryKeys ?? null);
    setFilterGroupName(options.groupName || null);
    setFilterSelectiveLogic(options.selectiveLogic as any || null);
    setFilterOrderMin(options.orderRange?.[0] ?? null);
    setFilterOrderMax(options.orderRange?.[1] ?? null);
    setFilterProbabilityMin(options.probabilityRange?.[0] ?? null);
    setFilterProbabilityMax(options.probabilityRange?.[1] ?? null);
  };

  const [deletingEntryId, setDeletingEntryId] = useState<string | null>(null);
  // const entryModal = useEntryModal();

  const {
    entries,
    entriesLoading,
    entriesHasMore,
    entriesTotal,
    entryLoadingIds,
    fetchEntries,
    loadMoreEntries,
    resetEntryState,
    createEntry,
    deleteEntry,
    bulkDeleteEntries,
    bulkToggleEntries,
    updateEntry,
  } = useWorldbookStore();

  // 当搜索条件变化时，重新搜索
  useEffect(() => {
    if (worldbookId) {
      // 重置并搜索
      resetEntryState();

      const searchParams = {
        page: 1,
        query: searchTerm || undefined,
        enabled: filterEnabled || undefined,
        hasKeys: filterHasKeys || undefined,
        hasSecondaryKeys: filterHasSecondaryKeys || undefined,
        groupName: filterGroupName || undefined,
        selectiveLogic: filterSelectiveLogic || undefined,
        orderMin: filterOrderMin || undefined,
        orderMax: filterOrderMax || undefined,
        probabilityMin: filterProbabilityMin || undefined,
        probabilityMax: filterProbabilityMax || undefined,
        sortBy,
        sortOrder,
      };

      fetchEntries(worldbookId, searchParams);
    }
  }, [
    worldbookId,
    searchTerm,
    filterEnabled,
    filterHasKeys,
    filterHasSecondaryKeys,
    filterGroupName,
    filterSelectiveLogic,
    filterOrderMin,
    filterOrderMax,
    filterProbabilityMin,
    filterProbabilityMax,
    sortBy,
    sortOrder,
    fetchEntries,
    resetEntryState,
  ]);



  const filteredEntries = useMemo(() => {
    let filtered = entries;

    // 搜索过滤
    if (searchTerm) {
      filtered = filtered.filter(entry =>
        entry.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.keys?.some(key => key.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // 高级过滤
    if (filterOptions.enabled !== null && filterOptions.enabled !== undefined) {
      filtered = filtered.filter(entry => entry.enabled === filterOptions.enabled);
    }



    if (filterOptions.selectiveLogic !== null && filterOptions.selectiveLogic !== undefined) {
      filtered = filtered.filter(entry => entry.selectiveLogic === filterOptions.selectiveLogic);
    }

    if (filterOptions.hasKeys !== null && filterOptions.hasKeys !== undefined) {
      filtered = filtered.filter(entry => {
        const hasKeys = entry.keys && entry.keys.length > 0;
        return hasKeys === filterOptions.hasKeys;
      });
    }

    if (filterOptions.hasSecondaryKeys !== null && filterOptions.hasSecondaryKeys !== undefined) {
      filtered = filtered.filter(entry => {
        const hasSecondaryKeys = entry.keysSecondary && entry.keysSecondary.length > 0;
        return hasSecondaryKeys === filterOptions.hasSecondaryKeys;
      });
    }

    if (filterOptions.orderRange) {
      const [min, max] = filterOptions.orderRange;
      filtered = filtered.filter(entry => {
        const order = entry.order || 0;
        return order >= min && order <= max;
      });
    }

    if (filterOptions.probabilityRange) {
      const [min, max] = filterOptions.probabilityRange;
      filtered = filtered.filter(entry => {
        const probability = entry.probability || 0;
        return probability >= min && probability <= max;
      });
    }

    if (filterOptions.groupName) {
      filtered = filtered.filter(entry =>
        entry.groupName?.toLowerCase().includes(filterOptions.groupName!.toLowerCase())
      );
    }

    // 排序
    filtered = [...filtered].sort((a, b) => {
      let comparison = 0;

      switch (sortOptions.sortBy) {
        case 'name':
          comparison = (a.title || '').localeCompare(b.title || '');
          break;
        case 'date':
          comparison = new Date(a.updatedAt || 0).getTime() - new Date(b.updatedAt || 0).getTime();
          break;
        case 'order':
          comparison = (a.order || 0) - (b.order || 0);
          break;
        case 'probability':
          comparison = (a.probability || 0) - (b.probability || 0);
          break;
      }

      return sortOptions.sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [entries, searchTerm, sortOptions, filterOptions]);

  const handleCreateEntry = () => {
    if (!worldbookId) return;
    onCreateEntry?.(worldbookId);
  };

  const handleEntrySelect = (entryId: string) => {
    // 现在点击条目是展开/关闭，不再打开弹窗
    // setEditingEntryId(entryId);
  };

  const handleEntryEdit = (entryId: string) => {
    if (!worldbookId) return;

    const entry = filteredEntries.find(e => e.id === entryId);
    if (!entry) return;

    onEditEntry?.(worldbookId, entry);
  };

  const handleEntryDelete = (entryId: string) => {
    setDeletingEntryId(entryId);
  };

  const handleEntryDuplicate = async (entryId: string) => {
    const entry = filteredEntries.find(e => e.id === entryId);
    if (!entry || !worldbookId) return;

    try {
      await createEntry(worldbookId, {
        title: `${entry.title} (副本)`,
        content: entry.content || '',
        keys: entry.keys || [],
        keysSecondary: entry.keysSecondary || [],
        selectiveLogic: entry.selectiveLogic,
        order: entry.order,
        probability: entry.probability,
        enabled: entry.enabled ?? true,
        caseSensitive: entry.caseSensitive ?? false,
        matchWholeWords: entry.matchWholeWords ?? false,
        useRegex: entry.useRegex || false,
      });
      message.success(t('copySuccess', { ns: 'common' }));
    } catch (error) {
      console.error('Failed to duplicate entry:', error);
      message.error('复制失败');
    }
  };

  const handleConfirmDelete = async () => {
    if (!deletingEntryId) return;

    try {
      await deleteEntry(deletingEntryId);
      // 如果删除的是选中的条目，从选中列表中移除
      setSelectedEntries(prev => prev.filter(id => id !== deletingEntryId));
      setDeletingEntryId(null);
    } catch (error) {
      console.error('Failed to delete entry:', error);
    }
  };

  const handleCancelDelete = () => {
    setDeletingEntryId(null);
  };

  const handleToggleSelection = (entryId: string) => {
    setSelectedEntries(prev =>
      prev.includes(entryId)
        ? prev.filter(id => id !== entryId)
        : [...prev, entryId]
    );
  };

  const handleSelectAll = () => {
    if (selectedEntries.length === filteredEntries.length) {
      setSelectedEntries([]);
    } else {
      setSelectedEntries(filteredEntries.map(entry => entry.id));
    }
  };

  const handleBulkDelete = async () => {
    if (selectedEntries.length === 0) return;

    if (confirm(t('entry.delete.confirmBatch', { count: selectedEntries.length }))) {
      await bulkDeleteEntries(selectedEntries);
      setSelectedEntries([]);
    }
  };

  const handleBulkToggleEnabled = async (enabled: boolean) => {
    if (selectedEntries.length === 0) return;

    await bulkToggleEntries(selectedEntries, enabled);
    setSelectedEntries([]);
  };



  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.searchRow}>
          <Input
            className={styles.searchInput}
            placeholder={t('entry.search.placeholder')}
            prefix={<Search size={16} />}
            suffix={
              <Button
                loading={entriesLoading}
                onClick={handleSearch}
                size="small"
              >
                搜索
              </Button>
            }
            value={inputValue}
            onChange={handleInputChange}
            onPressEnter={handleKeyPress}
          />
          <SortSelector
            value={sortOptions}
            onChange={setSortOptions}
          />
          <AdvancedFilter
            value={filterOptions}
            onChange={setFilterOptions}
          />
        </div>
      </div>

      {/* 工具栏 - 包含批量操作和创建按钮 */}
      <div className={styles.toolbar}>
        <div className={styles.toolbarLeft}>
          <Checkbox
            checked={selectedEntries.length === filteredEntries.length && filteredEntries.length > 0}
            indeterminate={selectedEntries.length > 0 && selectedEntries.length < filteredEntries.length}
            onChange={handleSelectAll}
          />
          {selectedEntries.length > 0 ? (
            <span>{t('entry.selected', { count: selectedEntries.length })}</span>
          ) : (
            <span>{t('entry.total', { count: filteredEntries.length })}</span>
          )}
        </div>

        <div className={styles.toolbarRight}>
          {selectedEntries.length > 0 && (
            <>
              <Button
                icon={<Eye size={14} />}
                onClick={() => handleBulkToggleEnabled(true)}
                size="small"
              >
                {t('entry.enable')}
              </Button>
              <Button
                icon={<EyeOff size={14} />}
                onClick={() => handleBulkToggleEnabled(false)}
                size="small"
              >
                {t('entry.disable')}
              </Button>
              <Button
                danger
                icon={<Trash2 size={14} />}
                onClick={handleBulkDelete}
                size="small"
              >
                {t('delete', { ns: 'common' })}
              </Button>
            </>
          )}

          <Button
            icon={<Plus size={14} />}
            onClick={handleCreateEntry}
            size="small"
            type="primary"
          >
            {t('entry.create.title')}
          </Button>
        </div>
      </div>

      <div className={styles.listContainer}>
        {entriesLoading ? (
          <SkeletonList />
        ) : filteredEntries.length === 0 ? (
          <EmptyStatus searchQuery={searchTerm} onCreateEntry={handleCreateEntry} />
        ) : (
          <VirtualizedList
            items={filteredEntries}
            keyExtractor={(entry) => entry.id}
            renderItem={(entry) => {
              const isDeleting = entryLoadingIds.includes(entry.id);
              return (
                <EntryItem
                  entry={entry}
                  isActive={false}
                  isSelected={selectedEntries.includes(entry.id)}
                  isSelectionMode={selectedEntries.length > 0}
                  onClick={() => handleEntrySelect(entry.id)}
                  onToggleSelection={() => handleToggleSelection(entry.id)}
                  onEdit={() => handleEntryEdit(entry.id)}
                  onDelete={() => handleEntryDelete(entry.id)}
                  onDuplicate={() => handleEntryDuplicate(entry.id)}
                  isDeleting={isDeleting}
                />
              );
            }}
            defaultItemHeight={120}
            overscan={5}
            style={{ height: '100%', width: '100%' }}
            onEndReached={() => {
              if (entriesHasMore && !entriesLoading && worldbookId) {
                loadMoreEntries(worldbookId);
              }
            }}
            onEndReachedThreshold={0.8}
          />
        )}
      </div>





      {/* 删除确认弹窗 */}
      <DeleteConfirmModal
        open={!!deletingEntryId}
        entry={deletingEntryId ? entries.find(e => e.id === deletingEntryId) || null : null}
        loading={deletingEntryId ? entryLoadingIds.includes(deletingEntryId) : false}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />
    </div>
  );
});

EntryList.displayName = 'EntryList';

export default EntryList;

import { and, desc, eq, inArray, ne } from 'drizzle-orm/expressions';

import {
  agents,
  agentsFiles,
  agentsKnowledgeBases,
  agentsToSessions,
  agentsWorldbooks,
  files,
  knowledgeBases,
  worldbooks,
} from '@/database/schemas';
import { LobeChatDatabase } from '@/database/type';

export class AgentModel {
  private userId: string;
  private db: LobeChatDatabase;

  constructor(db: LobeChatDatabase, userId: string) {
    this.userId = userId;
    this.db = db;
  }

  getAgentConfigById = async (id: string) => {
    const agent = await this.db.query.agents.findFirst({ where: eq(agents.id, id) });

    const knowledge = await this.getAgentAssignedKnowledge(id);

    return { ...agent, ...knowledge };
  };

  getAgentAssignedKnowledge = async (id: string) => {
    const knowledgeBaseResult = await this.db
      .select({ enabled: agentsKnowledgeBases.enabled, knowledgeBases })
      .from(agentsKnowledgeBases)
      .where(eq(agentsKnowledgeBases.agentId, id))
      .orderBy(desc(agentsKnowledgeBases.createdAt))
      .leftJoin(knowledgeBases, eq(knowledgeBases.id, agentsKnowledgeBases.knowledgeBaseId));

    const fileResult = await this.db
      .select({ enabled: agentsFiles.enabled, files })
      .from(agentsFiles)
      .where(eq(agentsFiles.agentId, id))
      .orderBy(desc(agentsFiles.createdAt))
      .leftJoin(files, eq(files.id, agentsFiles.fileId));

    const worldbookResult = await this.db
      .select({
        enabled: agentsWorldbooks.enabled,
        isPrimary: agentsWorldbooks.isPrimary,
        worldbooks
      })
      .from(agentsWorldbooks)
      .where(eq(agentsWorldbooks.agentId, id))
      .orderBy(desc(agentsWorldbooks.createdAt))
      .leftJoin(worldbooks, eq(worldbooks.id, agentsWorldbooks.worldbookId));

    return {
      files: fileResult.map((item) => ({
        ...item.files,
        enabled: item.enabled,
      })),
      knowledgeBases: knowledgeBaseResult.map((item) => ({
        ...item.knowledgeBases,
        enabled: item.enabled,
      })),
      worldbooks: worldbookResult.map((item) => ({
        ...item.worldbooks,
        enabled: item.enabled,
        isPrimary: item.isPrimary,
      })),
    };
  };

  /**
   * Find agent by session id
   */
  findBySessionId = async (sessionId: string) => {
    const item = await this.db.query.agentsToSessions.findFirst({
      where: eq(agentsToSessions.sessionId, sessionId),
    });
    if (!item) return;

    const agentId = item.agentId;

    return this.getAgentConfigById(agentId);
  };

  createAgentKnowledgeBase = async (
    agentId: string,
    knowledgeBaseId: string,
    enabled: boolean = true,
  ) => {
    return this.db.insert(agentsKnowledgeBases).values({
      agentId,
      enabled,
      knowledgeBaseId,
      userId: this.userId,
    });
  };

  deleteAgentKnowledgeBase = async (agentId: string, knowledgeBaseId: string) => {
    return this.db
      .delete(agentsKnowledgeBases)
      .where(
        and(
          eq(agentsKnowledgeBases.agentId, agentId),
          eq(agentsKnowledgeBases.knowledgeBaseId, knowledgeBaseId),
          eq(agentsKnowledgeBases.userId, this.userId),
        ),
      );
  };

  toggleKnowledgeBase = async (agentId: string, knowledgeBaseId: string, enabled?: boolean) => {
    return this.db
      .update(agentsKnowledgeBases)
      .set({ enabled })
      .where(
        and(
          eq(agentsKnowledgeBases.agentId, agentId),
          eq(agentsKnowledgeBases.knowledgeBaseId, knowledgeBaseId),
          eq(agentsKnowledgeBases.userId, this.userId),
        ),
      );
  };

  createAgentWorldbook = async (
    agentId: string,
    worldbookId: string,
    enabled: boolean = true,
  ) => {
    return this.db.insert(agentsWorldbooks).values({
      agentId,
      enabled,
      userId: this.userId,
      worldbookId,
    });
  };

  deleteAgentWorldbook = async (agentId: string, worldbookId: string) => {
    return this.db
      .delete(agentsWorldbooks)
      .where(
        and(
          eq(agentsWorldbooks.agentId, agentId),
          eq(agentsWorldbooks.worldbookId, worldbookId),
          eq(agentsWorldbooks.userId, this.userId),
        ),
      );
  };

  toggleWorldbook = async (agentId: string, worldbookId: string, enabled?: boolean, isPrimary?: boolean) => {
    // 参数验证 - 确保至少提供一个参数
    if (enabled === undefined && isPrimary === undefined) {
      throw new Error('INVALID_PARAMETERS');
    }

    // 统一使用事务处理 - 确保数据一致性
    return this.db.transaction(async (tx) => {
      // 如果设置为主要，需要清除冲突的主要标记
      if (isPrimary === true) {
        // 1. 清除该agent的其他主要worldbook标记
        await tx
          .update(agentsWorldbooks)
          .set({ isPrimary: false })
          .where(
            and(
              eq(agentsWorldbooks.agentId, agentId),
              ne(agentsWorldbooks.worldbookId, worldbookId),
              eq(agentsWorldbooks.userId, this.userId)
            )
          );
      }

      // 构建更新数据 - 只更新提供的字段
      const updateData: { enabled?: boolean; isPrimary?: boolean } = {};
      if (enabled !== undefined) updateData.enabled = enabled;
      if (isPrimary !== undefined) updateData.isPrimary = isPrimary;

      // 执行更新
      return tx
        .update(agentsWorldbooks)
        .set(updateData)
        .where(
          and(
            eq(agentsWorldbooks.agentId, agentId),
            eq(agentsWorldbooks.worldbookId, worldbookId),
            eq(agentsWorldbooks.userId, this.userId)
          )
        );
    });
  };

  createAgentFiles = async (agentId: string, fileIds: string[], enabled: boolean = true) => {
    // Exclude the fileIds that already exist in agentsFiles, and then insert them
    const existingFiles = await this.db
      .select({ id: agentsFiles.fileId })
      .from(agentsFiles)
      .where(
        and(
          eq(agentsFiles.agentId, agentId),
          eq(agentsFiles.userId, this.userId),
          inArray(agentsFiles.fileId, fileIds),
        ),
      );

    const existingFilesIds = new Set(existingFiles.map((item) => item.id));

    const needToInsertFileIds = fileIds.filter((fileId) => !existingFilesIds.has(fileId));

    if (needToInsertFileIds.length === 0) return;

    return this.db
      .insert(agentsFiles)
      .values(
        needToInsertFileIds.map((fileId) => ({ agentId, enabled, fileId, userId: this.userId })),
      );
  };

  deleteAgentFile = async (agentId: string, fileId: string) => {
    return this.db
      .delete(agentsFiles)
      .where(
        and(
          eq(agentsFiles.agentId, agentId),
          eq(agentsFiles.fileId, fileId),
          eq(agentsFiles.userId, this.userId),
        ),
      );
  };

  toggleFile = async (agentId: string, fileId: string, enabled?: boolean) => {
    return this.db
      .update(agentsFiles)
      .set({ enabled })
      .where(
        and(
          eq(agentsFiles.agentId, agentId),
          eq(agentsFiles.fileId, fileId),
          eq(agentsFiles.userId, this.userId),
        ),
      );
  };
}

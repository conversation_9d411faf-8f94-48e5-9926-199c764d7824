import { and, desc, eq, ne, notExists } from 'drizzle-orm';

import { LobeChatDatabase, Transaction } from '../type';
import type {
  CreateWorldbookParams,
  UpdateWorldbookParams,
  Worldbook,
} from '../../types/worldbook';

import { NewWorldbook, WorldbookItem, worldbooks } from '../schemas';
import { worldbookEntries } from '../schemas/worldbookEntry';
import { agentsWorldbooks } from '../schemas/agent';
import { WorldbookEntryModel } from './worldbookEntry';

export class WorldbookModel {
  private userId: string;
  private db: LobeChatDatabase;

  constructor(db: LobeChatDatabase, userId: string) {
    this.userId = userId;
    this.db = db;
  }

  // ====== CRUD Operations ======

  /**
   * 创建世界书
   */
  create = async (params: CreateWorldbookParams): Promise<WorldbookItem> => {
    const [result] = await this.db
      .insert(worldbooks)
      .values({
        description: params.description,
        enabled: true,
        name: params.name,
        userId: this.userId,
      } as NewWorldbook)
      .returning();

    return result;
  };

  /**
   * 更新世界书
   */
  update = async (id: string, params: UpdateWorldbookParams): Promise<WorldbookItem | null> => {
    const [result] = await this.db
      .update(worldbooks)
      .set({
        ...params,
        updatedAt: new Date(),
      })
      .where(and(eq(worldbooks.id, id), eq(worldbooks.userId, this.userId)))
      .returning();

    if (!result) return null;
    return result;
  };

  /**
   * 删除世界书及其所有相关数据
   */
  delete = async (id: string, trx?: Transaction): Promise<void> => {
    const executeDelete = async (tx: Transaction) => {
      // 1. 删除所有相关的条目（这会级联删除相关数据）
      await tx
        .delete(worldbookEntries)
        .where(and(eq(worldbookEntries.worldbookId, id), eq(worldbookEntries.userId, this.userId)));

      // 2. 删除世界书记录
      await tx
        .delete(worldbooks)
        .where(and(eq(worldbooks.id, id), eq(worldbooks.userId, this.userId)));
    };

    // 如果已经在事务中，直接执行；否则创建新事务
    if (trx) {
      await executeDelete(trx);
    } else {
      await this.db.transaction(executeDelete);
    }
  };


  // ====== Query Operations ======

  /**
   * 查询用户的所有世界书
   */
  query = async (): Promise<Worldbook[]> => {
    const data = await this.db.query.worldbooks.findMany({
      orderBy: desc(worldbooks.createdAt),
      where: eq(worldbooks.userId, this.userId),
      with: {
        agentsWorldbook: {
          where: and(
            eq(agentsWorldbooks.isPrimary, true),
            eq(agentsWorldbooks.enabled, true),
            eq(agentsWorldbooks.userId, this.userId)  // 用户隔离
          ),
          with: {
            agent: {
              columns: {
                id: true,
                title: true,
              },
            },
          },
        },
      },
    });

    return data.map(item => ({
      description: (item.description as string | null) || undefined,
      enabled: item.enabled as boolean,
      id: item.id as string,
      name: item.name as string,
      // 专属agent信息
      primaryAgent: item.agentsWorldbook[0]?.agent ? {
        id: item.agentsWorldbook[0].agent.id as string,
        title: (item.agentsWorldbook[0].agent.title as string | null) || '未命名Agent'
      } : undefined,
    }));
  };

  /**
   * 查询未被其他agent设置为主要的世界书（排除指定agent）
   * @param excludeAgentId - 要排除的Agent ID
   * @returns 未被其他agent设置为主要的世界书列表
   */
  queryNonPrimaryForAgent = async (excludeAgentId: string): Promise<Worldbook[]> => {
    const data = await this.db.query.worldbooks.findMany({
      orderBy: desc(worldbooks.createdAt),
      where: and(
        eq(worldbooks.userId, this.userId),
        eq(worldbooks.enabled, true),
        notExists(
          this.db
            .select()
            .from(agentsWorldbooks)
            .where(
              and(
                eq(agentsWorldbooks.worldbookId, worldbooks.id),
                ne(agentsWorldbooks.agentId, excludeAgentId), // 排除指定的agent
                eq(agentsWorldbooks.isPrimary, true),
                eq(agentsWorldbooks.userId, this.userId)
              )
            )
        )
      ),
    });

    return data.map(item => ({
      description: (item.description as string | null) || undefined,
      enabled: item.enabled as boolean,
      id: item.id as string,
      name: item.name as string,
      primaryAgent: undefined,
    }));
  };

  /**
   * 根据ID查找世界书
   */
  findById = async (id: string): Promise<WorldbookItem | null> => {
    const data = await this.db.query.worldbooks.findFirst({
      where: and(eq(worldbooks.id, id), eq(worldbooks.userId, this.userId)),
    });

    if (!data) return null;
    return data;
  };

  /**
   * 根据ID查找世界书（包含条目信息）
   */
  findByIdWithEntries = async (id: string) => {
    return this.db.query.worldbooks.findFirst({
      where: and(eq(worldbooks.id, id), eq(worldbooks.userId, this.userId)),
      with: {
        worldbookEntry: {
          orderBy: [desc(worldbookEntries.orderIndex)],
          where: eq(worldbookEntries.enabled, true),
        },
      },
    });
  };


  // ====== Import/Export Operations ======

  /**
   * 导入世界书（创建世界书和条目）
   */
  importWorldbook = async (data: {
    entries: any[]; // 使用any[]，具体类型由WorldbookEntryModel处理
    worldbook: CreateWorldbookParams;
  }) => {
    return this.db.transaction(async (trx) => {
      // 1. 创建世界书
      const worldbook = await this.create(data.worldbook);

      // 2. 批量创建条目
      const entriesWithWorldbookId = data.entries.map(entry => ({
        ...entry,
        worldbookId: worldbook.id,
      }));

      // 使用WorldbookEntryModel进行批量创建
      const worldbookEntryModel = new WorldbookEntryModel(trx, this.userId);
      await worldbookEntryModel.bulkCreate(entriesWithWorldbookId);

      return {
        totalEntries: data.entries.length,
        worldbookId: worldbook.id,
        worldbookName: worldbook.name,
      };
    });
  };

}

// 世界书条目相关类型定义
import { z } from 'zod';
import { ActivationMode, SelectiveLogic, WorldbookPosition } from './enums';

/**
 * 匹配源配置Zod schema
 * 定义世界书条目可以匹配的内容源
 */
export const MatchSourcesSchema = z.object({
  /** 匹配角色深度提示 */
  characterDepthPrompt: z.boolean().default(false),
  /** 匹配角色描述 */
  characterDescription: z.boolean().default(false),
  /** 匹配角色性格 */
  characterPersonality: z.boolean().default(false),
  /** 匹配创作者备注 */
  creatorNotes: z.boolean().default(false),
  /** 匹配人格描述 */
  personaDescription: z.boolean().default(false),
  /** 匹配场景 */
  scenario: z.boolean().default(false),
});

/**
 * 创建世界书条目Zod schema
 */
export const CreateWorldbookEntrySchema = z.object({
  activationMode: z.nativeEnum(ActivationMode).default(ActivationMode.Keyword),
  addMemo: z.boolean().default(false),
  caseSensitive: z.boolean().default(false),
  characterFilterExclude: z.boolean().default(false),
  characterFilterNames: z.array(z.string()).default([]),
  characterFilterTags: z.array(z.string()).default([]),
  content: z.string().min(1),
  cooldown: z.number().int().min(0).optional(),
  decorators: z.array(z.string()).default([]),
  delay: z.number().int().min(0).optional(),
  delayUntilRecursion: z.number().int().min(0).default(0),
  depth: z.number().int().min(0).max(20).default(4),
  displayIndex: z.number().int().default(0),
  excludeRecursion: z.boolean().default(false),
  groupName: z.string().optional(),
  groupOverride: z.boolean().default(false),
  groupWeight: z.number().min(0).max(100).default(100),
  keys: z.array(z.string()).default([]),
  keysSecondary: z.array(z.string()).default([]),
  matchSources: MatchSourcesSchema.default({}),
  matchWholeWords: z.boolean().default(false),
  order: z.number().int().default(100),
  position: z.nativeEnum(WorldbookPosition).default(WorldbookPosition.After),
  preventRecursion: z.boolean().default(false),
  probability: z.number().min(0).max(100).default(100),
  role: z.enum(['user', 'system', 'assistant', 'tool']).default('system'),
  scanDepth: z.number().int().min(0).max(1000).default(100),
  selectiveLogic: z.nativeEnum(SelectiveLogic).default(SelectiveLogic.AND_ANY),
  sticky: z.number().int().min(0).optional(),
  title: z.string().optional(),
  useGroupScoring: z.boolean().default(false),
  useProbability: z.boolean().default(true),
  useRegex: z.boolean().default(false),
  worldbookId: z.string().min(1),
});

/**
 * 更新世界书条目Zod schema
 */
export const UpdateWorldbookEntrySchema = CreateWorldbookEntrySchema.omit({ worldbookId: true }).partial().extend({
  enabled: z.boolean().optional(),
});





/**
 * 搜索参数Zod schema
 */
export const SearchParamsSchema = z.object({
  activationMode: z.nativeEnum(ActivationMode).optional(),
  enabled: z.boolean().optional(),
  page: z.number().int().positive().optional(),
  pageSize: z.number().int().positive().max(100).optional(),
  query: z.string().optional(),
  sortBy: z.enum(['orderIndex', 'title', 'createdAt', 'updatedAt', 'probability']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

/**
 * 分页响应Zod schema工厂函数
 */
export const PaginatedResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) => z.object({
  data: z.array(dataSchema),
  hasMore: z.boolean(),
  page: z.number().int().positive(),
  pageSize: z.number().int().positive(),
  total: z.number().int().min(0),
});

// 使用z.infer生成TypeScript类型
export type MatchSources = z.infer<typeof MatchSourcesSchema>;
export type CreateWorldbookEntryParams = z.infer<typeof CreateWorldbookEntrySchema>;
export type UpdateWorldbookEntryParams = z.infer<typeof UpdateWorldbookEntrySchema>;
export type SearchParams = z.infer<typeof SearchParamsSchema>;

/**
 * 分页响应泛型接口
 * 注：泛型类型在项目中通常直接定义为interface，而不是使用z.infer
 */
export interface PaginatedResponse<T> {
  data: T[];
  hasMore: boolean;
  page: number;
  pageSize: number;
  total: number;
}

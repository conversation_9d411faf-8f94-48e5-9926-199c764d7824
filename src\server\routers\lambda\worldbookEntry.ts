import { z } from 'zod';

import { WorldbookEntryModel } from '@/database/models/worldbookEntry';
import { authedProcedure, router } from '@/libs/trpc/lambda';
import { serverDatabase } from '@/libs/trpc/lambda/middleware';
import type { WorldbookEntry } from '@/database/schemas/worldbookEntry';
import {
  CreateWorldbookEntrySchema,
  SearchParamsSchema,
  UpdateWorldbookEntrySchema,
  type PaginatedResponse,
} from '@/types/worldbook/entry';

// 直接使用导入的schemas，无需创建别名

const worldbookEntryProcedure = authedProcedure.use(serverDatabase).use(async (opts) => {
  const { ctx } = opts;
  return opts.next({
    ctx: {
      entryModel: new WorldbookEntryModel(ctx.serverDB, ctx.userId),
    },
  });
});

export const worldbookEntryRouter = router({
  // ====== Batch Operations ======

  /**
   * 批量创建条目
   */
  bulkCreateEntries: worldbookEntryProcedure
    .input(
      z.object({
        entries: z.array(CreateWorldbookEntrySchema.omit({ worldbookId: true })).min(1, '至少需要一个条目'),
        worldbookId: z.string().min(1, '世界书ID不能为空'),
      }),
    )
    .mutation(async ({ input, ctx }): Promise<WorldbookEntry[]> => {
      const entriesWithWorldbookId = input.entries.map(entry => ({
        ...entry,
        worldbookId: input.worldbookId,
      }));

      return ctx.entryModel.bulkCreate(entriesWithWorldbookId);
    }),

  /**
   * 批量删除条目
   */
  bulkDeleteEntries: worldbookEntryProcedure
    .input(z.object({ ids: z.array(z.number().int().positive()).min(1) }))
    .mutation(async ({ input, ctx }): Promise<number> => {
      return ctx.entryModel.bulkDelete(input.ids);
    }),

  /**
   * 批量启用/禁用条目
   */
  bulkToggleEntries: worldbookEntryProcedure
    .input(
      z.object({
        enabled: z.boolean(),
        ids: z.array(z.number().int().positive()).min(1),
      }),
    )
    .mutation(async ({ input, ctx }): Promise<number> => {
      return ctx.entryModel.bulkToggle(input.ids, input.enabled);
    }),

  // ====== CRUD Operations ======

  /**
   * 创建条目
   */
  createEntry: worldbookEntryProcedure
    .input(CreateWorldbookEntrySchema)
    .mutation(async ({ input, ctx }): Promise<WorldbookEntry> => {
      return ctx.entryModel.create(input);
    }),

  /**
   * 删除条目
   */
  deleteEntry: worldbookEntryProcedure
    .input(z.object({ id: z.number().int().positive() }))
    .mutation(async ({ ctx, input }): Promise<void> => {
      await ctx.entryModel.delete(input.id);
    }),

  /**
   * 获取世界书的所有条目
   */
  getEntriesByWorldbookId: worldbookEntryProcedure
    .input(z.object({ worldbookId: z.string().min(1, '世界书ID不能为空') }))
    .query(async ({ ctx, input }): Promise<WorldbookEntry[]> => {
      return ctx.entryModel.findByWorldbookId(input.worldbookId);
    }),

  /**
   * 根据ID获取条目
   */
  getEntryById: worldbookEntryProcedure
    .input(z.object({ id: z.number().int().positive() }))
    .query(async ({ ctx, input }): Promise<WorldbookEntry | null> => {
      return ctx.entryModel.findById(input.id);
    }),

  /**
   * 分页查询条目（支持搜索和过滤）
   */
  searchEntries: worldbookEntryProcedure
    .input(
      z.object({
        params: SearchParamsSchema.optional(),
        worldbookId: z.string().min(1, '世界书ID不能为空'),
      }),
    )
    .query(async ({ ctx, input }): Promise<PaginatedResponse<WorldbookEntry>> => {
      return ctx.entryModel.findByWorldbookIdWithParams(input.worldbookId, input.params || {});
    }),

  /**
   * 更新条目
   */
  updateEntry: worldbookEntryProcedure
    .input(
      z.object({
        data: UpdateWorldbookEntrySchema,
        id: z.number().int().positive(),
      }),
    )
    .mutation(async ({ input, ctx }): Promise<WorldbookEntry | null> => {
      return ctx.entryModel.update(input.id, input.data);
    }),
});

import { z } from 'zod';

import { WorldbookModel } from '@/database/models/worldbook';
import { authedProcedure, router } from '@/libs/trpc/lambda';
import { serverDatabase } from '@/libs/trpc/lambda/middleware';
import {
  CreateWorldbookSchema,
  ImportWorldbookSchema,
  UpdateWorldbookSchema,
  Worldbook,
  type ImportResultDetail,
} from '@/types/worldbook';
import type { WorldbookItem } from '@/database/schemas/worldbook';

const worldbookProcedure = authedProcedure.use(serverDatabase).use(async (opts) => {
  const { ctx } = opts;

  return opts.next({
    ctx: {
      worldbookModel: new WorldbookModel(ctx.serverDB, ctx.userId),
    },
  });
});

export const worldbookRouter = router({
  // ====== CRUD Operations ======

  /**
   * 创建世界书
   */
  createWorldbook: worldbookProcedure
    .input(CreateWorldbookSchema)
    .mutation(async ({ input, ctx }): Promise<WorldbookItem> => {
      const result = await ctx.worldbookModel.create(input);
      return result;
    }),

  /**
   * 删除世界书及其所有相关数据
   */
  deleteWorldbook: worldbookProcedure
    .input(z.object({ id: z.string().min(1, 'ID不能为空') }))
    .mutation(async ({ input, ctx }): Promise<void> => {
      await ctx.worldbookModel.delete(input.id);
    }),

  /**
   * 根据ID获取世界书
   */
  getWorldbookById: worldbookProcedure
    .input(z.object({ id: z.string().min(1, 'ID不能为空') }))
    .query(async ({ ctx, input }): Promise<WorldbookItem | null> => {
      const result = await ctx.worldbookModel.findById(input.id);
      return result;
    }),

  /**
   * 获取用户的所有世界书
   */
  getWorldbooks: worldbookProcedure.query(async ({ ctx }): Promise<Worldbook[]> => {
    const result = await ctx.worldbookModel.query();
    return result;
  }),

  // ====== Import/Export Operations ======

  /**
   * 导入世界书（创建世界书和条目）
   */
  importWorldbook: worldbookProcedure
    .input(ImportWorldbookSchema)
    .mutation(async ({ input, ctx }): Promise<ImportResultDetail> => {
      const result = await ctx.worldbookModel.importWorldbook(input);
      return result;
    }),

  /**
   * 更新世界书
   */
  updateWorldbook: worldbookProcedure
    .input(
      z.object({
        data: UpdateWorldbookSchema,
        id: z.string().min(1, 'ID不能为空'),
      }),
    )
    .mutation(async ({ input, ctx }): Promise<WorldbookItem | null> => {
      const result = await ctx.worldbookModel.update(input.id, input.data);
      return result;
    }),
});

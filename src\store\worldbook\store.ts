import { devtools } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';
import { createWithEqualityFn } from 'zustand/traditional';

import { type WorldbookStoreState, initialState } from './initialState';
import { worldbookSelectors, entrySelectors, combinedSelectors } from './selectors';
import { type WorldbookAction, createWorldbookSlice } from './slices/worldbook/action';
import { type WorldbookEntryAction, createWorldbookEntrySlice } from './slices/entry/action';

/**
 * 世界书Store类型定义
 * 组合状态、世界书操作和条目操作
 */
export type WorldbookStore = WorldbookStoreState & WorldbookAction & WorldbookEntryAction;

/**
 * 导出所有选择器以便使用
 */
export {
  worldbookSelectors,
  entrySelectors,
  combinedSelectors,
};

/**
 * 创建世界书Store实例
 * 使用Zustand的devtools中间件进行调试
 * 使用shallow比较优化性能
 */
export const useWorldbookStore = createWithEqualityFn<WorldbookStore>()(
  devtools(
    (...args) => ({
      ...initialState,
      ...createWorldbookSlice(...args),
      ...createWorldbookEntrySlice(...args),
    }),
    {
      name: 'LobeChat_Worldbook',
      // 启用Redux DevTools的时间旅行功能
      serialize: true,
    },
  ),
  shallow,
);

/**
 * Store使用示例和最佳实践
 *
 * 1. 基础使用：
 * ```tsx
 * const worldbooks = useWorldbookStore(worldbookSelectors.worldbooks);
 * const fetchWorldbooks = useWorldbookStore(s => s.fetchWorldbooks);
 * ```
 *
 * 2. 组合选择器：
 * ```tsx
 * const currentStats = useWorldbookStore(combinedSelectors.currentWorldbookStats);
 * const hasLoading = useWorldbookStore(combinedSelectors.hasAnyLoading);
 * ```
 *
 * 3. 条件选择器：
 * ```tsx
 * const isLoading = useWorldbookStore(s => s.isWorldbookLoading('worldbook-id'));
 * const entry = useWorldbookStore(s => s.getEntryById('entry-id'));
 * ```
 *
 * 4. 批量操作：
 * ```tsx
 * const selectedIds = useWorldbookStore(entrySelectors.selectedEntryIds);
 * const bulkDelete = useWorldbookStore(s => s.bulkDeleteEntries);
 *
 * const handleBulkDelete = async () => {
 *   if (selectedIds.length > 0) {
 *     await bulkDelete(selectedIds);
 *   }
 * };
 * ```
 *
 * 5. 搜索和过滤：
 * ```tsx
 * const setSearchParams = useWorldbookStore(s => s.setSearchParams);
 * const searchParams = useWorldbookStore(entrySelectors.searchParams);
 *
 * const handleSearch = (query: string) => {
 *   setSearchParams({ query, page: 1 });
 * };
 * ```
 */

/**
 * 常用的组合Hook
 * 提供常见的状态组合，减少重复代码
 */

/**
 * 世界书管理Hook
 * 返回世界书管理相关的状态和操作
 */
export const useWorldbookManager = () => {
  return useWorldbookStore((state) => ({
    // 数据
    worldbooks: state.worldbooks,
    currentWorldbook: state.currentWorldbook,
    stats: state.stats,

    // 状态
    loading: state.loading,
    importing: state.importing,

    // 操作
    fetchWorldbooks: state.fetchWorldbooks,
    createWorldbook: state.createWorldbook,
    updateWorldbook: state.updateWorldbook,
    deleteWorldbook: state.deleteWorldbook,
    setCurrentWorldbook: state.setCurrentWorldbook,
    importWorldbook: state.importWorldbook,
  }));
};

/**
 * 条目管理Hook
 * 返回条目管理相关的状态和操作
 */
export const useEntryManager = () => {
  return useWorldbookStore((state) => ({
    // 数据
    entries: state.entries,
    currentEntry: state.currentEntry,
    selectedEntryIds: state.selectedEntryIds,

    // 分页
    entriesTotal: state.entriesTotal,
    entriesPage: state.entriesPage,
    entriesHasMore: state.entriesHasMore,

    // 状态
    entriesLoading: state.entriesLoading,
    bulkOperationLoading: state.bulkOperationLoading,

    // 操作
    fetchEntries: state.fetchEntries,
    createEntry: state.createEntry,
    updateEntry: state.updateEntry,
    deleteEntry: state.deleteEntry,
    setCurrentEntry: state.setCurrentEntry,

    // 选择
    toggleEntrySelection: state.toggleEntrySelection,
    selectAllEntries: state.selectAllEntries,
    clearEntrySelection: state.clearEntrySelection,

    // 批量操作
    bulkDeleteEntries: state.bulkDeleteEntries,
    bulkUpdateEntries: state.bulkUpdateEntries,
    bulkToggleEntries: state.bulkToggleEntries,

    // 分页
    loadMoreEntries: state.loadMoreEntries,
    refreshEntries: state.refreshEntries,
  }));
};

/**
 * 搜索和过滤Hook
 * 返回搜索和过滤相关的状态和操作
 */
export const useSearchAndFilter = () => {
  return useWorldbookStore((state) => ({
    // 搜索参数
    searchParams: state.searchParams,
    filterEnabled: state.filterEnabled,
    sortBy: state.sortBy,
    sortOrder: state.sortOrder,

    // 状态
    hasActiveSearch: combinedSelectors.hasActiveSearch(state),
    hasActiveFilter: combinedSelectors.hasActiveFilter(state),

    // 操作
    setSearchParams: state.setSearchParams,
    resetSearchParams: state.resetSearchParams,
    setFilterEnabled: state.setFilterEnabled,
    setSortBy: state.setSortBy,
    setSortOrder: state.setSortOrder,
  }));
};

/**
 * UI状态Hook
 * 返回UI相关的状态和操作
 */
export const useWorldbookUI = () => {
  return useWorldbookStore((state) => ({
    // UI状态
    sidebarCollapsed: state.sidebarCollapsed,
    entryEditorVisible: state.entryEditorVisible,
    worldbookManagerVisible: state.worldbookManagerVisible,

    // 操作
    toggleSidebar: state.toggleSidebar,
    setEntryEditorVisible: state.setEntryEditorVisible,
    setWorldbookManagerVisible: state.setWorldbookManagerVisible,
  }));
};

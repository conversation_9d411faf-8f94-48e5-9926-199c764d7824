/* eslint-disable sort-keys-fix/sort-keys-fix  */
import {
  boolean,
  jsonb,
  pgTable,
  primaryKey,
  text,
  uniqueIndex,
  varchar,
} from 'drizzle-orm/pg-core';
import { eq } from 'drizzle-orm';
import { createInsertSchema } from 'drizzle-zod';

import { idGenerator, randomSlug } from '@/database/utils/idGenerator';
import { LobeAgentChatConfig, LobeAgentPromptConfig, LobeAgentTTSConfig, LobeAgentWorldBookMeta } from '@/types/agent';

import { timestamps } from './_helpers';
import { files, knowledgeBases } from './file';
import { users } from './user';
import { worldbooks } from './worldbook';

// Agent table is the main table for storing agents
// agent is a model that represents the assistant that is created by the user
// agent can have its own knowledge base and files

export const agents = pgTable(
  'agents',
  {
    id: text('id')
      .primaryKey()
      .$defaultFn(() => idGenerator('agents'))
      .notNull(),
    slug: varchar('slug', { length: 100 })
      .$defaultFn(() => randomSlug(4))
      .unique(),
    title: text('title'),
    description: text('description'),
    tags: jsonb('tags').$type<string[]>().default([]),
    avatar: text('avatar'),
    backgroundColor: text('background_color'),

    plugins: jsonb('plugins').$type<string[]>().default([]),

    clientId: text('client_id'),

    userId: text('user_id')
      .references(() => users.id, { onDelete: 'cascade' })
      .notNull(),

    chatConfig: jsonb('chat_config').$type<LobeAgentChatConfig>(),

    fewShots: jsonb('few_shots'),
    model: text('model'),
    params: jsonb('params').default({}),
    provider: text('provider'),
    systemRole: text('system_role'),
    tts: jsonb('tts').$type<LobeAgentTTSConfig>(),
    roleFirstMsgs: jsonb('role_first_msgs').$type<string[]>().default([]),
    preset: text('preset'),

    authorAvatar: text('author_avatar'),
    authorName: text('author_name'),
    authorNotes: text('author_notes'),
    authorUid: text('author_uid'),
    promptConfig: jsonb('prompt_config').$type<LobeAgentPromptConfig>(),
    worldBook: jsonb('world_book').$type<LobeAgentWorldBookMeta>(),

    openingMessage: text('opening_message'),
    openingQuestions: text('opening_questions').array().default([]),

    ...timestamps,
  },
  (t) => ({
    clientIdUnique: uniqueIndex('client_id_user_id_unique').on(t.clientId, t.userId),
  }),
);

export const insertAgentSchema = createInsertSchema(agents);

export type NewAgent = typeof agents.$inferInsert;
export type AgentItem = typeof agents.$inferSelect;

export const agentsKnowledgeBases = pgTable(
  'agents_knowledge_bases',
  {
    agentId: text('agent_id')
      .references(() => agents.id, { onDelete: 'cascade' })
      .notNull(),
    knowledgeBaseId: text('knowledge_base_id')
      .references(() => knowledgeBases.id, { onDelete: 'cascade' })
      .notNull(),
    userId: text('user_id')
      .references(() => users.id, { onDelete: 'cascade' })
      .notNull(),
    enabled: boolean('enabled').default(true),

    ...timestamps,
  },
  (t) => ({
    pk: primaryKey({ columns: [t.agentId, t.knowledgeBaseId] }),
  }),
);

export const agentsFiles = pgTable(
  'agents_files',
  {
    fileId: text('file_id')
      .notNull()
      .references(() => files.id, { onDelete: 'cascade' }),
    agentId: text('agent_id')
      .notNull()
      .references(() => agents.id, { onDelete: 'cascade' }),
    enabled: boolean('enabled').default(true),
    userId: text('user_id')
      .references(() => users.id, { onDelete: 'cascade' })
      .notNull(),

    ...timestamps,
  },
  (t) => ({
    pk: primaryKey({ columns: [t.fileId, t.agentId, t.userId] }),
  }),
);

export const agentsWorldbooks = pgTable(
  'agents_worldbooks',
  {
    agentId: text('agent_id')
      .references(() => agents.id, { onDelete: 'cascade' })
      .notNull(),
    worldbookId: text('worldbook_id')
      .references(() => worldbooks.id, { onDelete: 'cascade' })
      .notNull(),
    userId: text('user_id')
      .references(() => users.id, { onDelete: 'cascade' })
      .notNull(),
    enabled: boolean('enabled').default(true),
    isPrimary: boolean('is_primary').default(false),

    ...timestamps,
  },
  (t) => ({
    pk: primaryKey({ columns: [t.agentId, t.worldbookId] }),
    // 确保一个worldbook只能被一个agent设置为主要
    worldbookPrimaryUnique: uniqueIndex('agents_worldbooks_worldbook_primary_unique')
      .on(t.worldbookId, t.userId)
      .where(eq(t.isPrimary, true)),
    // 确保一个agent只能有一个主要worldbook（保留原有约束）
    agentPrimaryUnique: uniqueIndex('agents_worldbooks_agent_primary_unique')
      .on(t.agentId, t.userId)
      .where(eq(t.isPrimary, true)),
  }),
);

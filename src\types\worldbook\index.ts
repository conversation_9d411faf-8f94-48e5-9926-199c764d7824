import { z } from 'zod';
import { CreateWorldbookEntrySchema } from './entry';

/**
 * 创建世界书Zod schema
 */
export const CreateWorldbookSchema = z.object({
  description: z.string().optional(),
  name: z.string().min(1, '世界书名称不能为空'),
});

/**
 * 更新世界书Zod schema
 */
export const UpdateWorldbookSchema = CreateWorldbookSchema.partial().extend({
  enabled: z.boolean().optional(),
});

/**
 * 世界书基础信息Zod schema
 */
export const WorldbookSchema = z.object({
  description: z.string().optional(),
  enabled: z.boolean(),
  id: z.string(),
  isPrimary: z.boolean().optional(),
  name: z.string(),
  primaryAgent: z.object({
    id: z.string(),
    title: z.string(),
  }).optional(),
});

// 使用z.infer生成TypeScript类型
export type CreateWorldbookParams = z.infer<typeof CreateWorldbookSchema>;
export type UpdateWorldbookParams = z.infer<typeof UpdateWorldbookSchema>;
export type Worldbook = z.infer<typeof WorldbookSchema>;



/**
 * 导入世界书Zod schema
 */
export const ImportWorldbookSchema = z.object({
  entries: z.array(CreateWorldbookEntrySchema.omit({ worldbookId: true })),
  worldbook: CreateWorldbookSchema,
});

/**
 * 导入结果详情Zod schema
 */
export const ImportResultDetailSchema = z.object({
  totalEntries: z.number().int().min(0),
  worldbookId: z.string(),
  worldbookName: z.string(),
});

// 使用z.infer生成TypeScript类型
export type ImportWorldbookParams = z.infer<typeof ImportWorldbookSchema>;
export type ImportResultDetail = z.infer<typeof ImportResultDetailSchema>;


// 批量操作已简化，不再需要复杂的结果类型

// 重新导出枚举类型
export * from './enums';

// 导出激活相关类型
// export * from './activation';

// 导出新的entry类型
export * from './entry';

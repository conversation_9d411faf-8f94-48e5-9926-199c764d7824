/**
 * This file contains the root router of Lobe Chat tRPC-backend
 */
import { publicProcedure, router } from '@/libs/trpc/lambda';

import { agentRouter } from './agent';
import { aiModelRouter } from './aiModel';
import { aiProviderRouter } from './aiProvider';
import { apiKeyRouter } from './apiKey';
import { aiPresetRouter } from './aiPreset';
import { chunkRouter } from './chunk';
import { configRouter } from './config';
import { documentRouter } from './document';
import { exporterRouter } from './exporter';
import { fileRouter } from './file';
import { generationRouter } from './generation';
import { generationBatchRouter } from './generationBatch';
import { generationTopicRouter } from './generationTopic';
import { imageRouter } from './image';
import { importerRouter } from './importer';
import { knowledgeBaseRouter } from './knowledgeBase';
import { marketRouter } from './market';
import { messageRouter } from './message';
import { pluginRouter } from './plugin';
import { ragEvalRouter } from './ragEval';
import { sessionRouter } from './session';
import { sessionGroupRouter } from './sessionGroup';
import { threadRouter } from './thread';
import { topicRouter } from './topic';
import { userRouter } from './user';
import { assistantRouter } from './assistant';
import { assistantLibraryRouter } from './assistantLibrary';
import { worldbookRouter } from './worldbook';
import { worldbookEntryRouter } from './worldbookEntry';

export const lambdaRouter = router({
  agent: agentRouter,
  aiModel: aiModelRouter,
  aiPreset: aiPresetRouter,
  aiProvider: aiProviderRouter,
  apiKey: apiKeyRouter,
  assistant: assistantRouter,
  assistantLibrary: assistantLibraryRouter,
  chunk: chunkRouter,
  config: configRouter,
  document: documentRouter,
  exporter: exporterRouter,
  file: fileRouter,
  generation: generationRouter,
  generationBatch: generationBatchRouter,
  generationTopic: generationTopicRouter,
  healthcheck: publicProcedure.query(() => "i'm live!"),
  image: imageRouter,
  importer: importerRouter,
  knowledgeBase: knowledgeBaseRouter,
  market: marketRouter,
  message: messageRouter,
  plugin: pluginRouter,
  ragEval: ragEvalRouter,
  session: sessionRouter,
  sessionGroup: sessionGroupRouter,
  thread: threadRouter,
  topic: topicRouter,
  user: userRouter,
  worldbook: worldbookRouter,
  worldbookEntry: worldbookEntryRouter,
});

export type LambdaRouter = typeof lambdaRouter;

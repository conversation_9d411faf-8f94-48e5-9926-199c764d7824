import {
  integer,
  pgTable,
  primaryKey,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { createInsertSchema } from 'drizzle-zod';

import { chunks } from './rag';
import { users } from './user';
import { worldbooks } from './worldbook';
import { worldbookEntries } from './worldbookEntry';

/**
 * worldbook_chunks表
 */
export const worldbookChunks = pgTable(
  'worldbook_chunks',
  {
    chunkId: uuid('chunk_id')
      .references(() => chunks.id, { onDelete: 'cascade' })
      .notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    userId: text('user_id')
      .references(() => users.id, { onDelete: 'cascade' })
      .notNull(),
      
    worldbookEntryId: integer('worldbook_entry_id')
      .references(() => worldbookEntries.id, { onDelete: 'cascade' })
      .notNull(),
    worldbookId: text('worldbook_id')
      .references(() => worldbooks.id, { onDelete: 'cascade' })
      .notNull(),
  },
  (table) => ({
    // 复合主键
    pk: primaryKey({ columns: [table.worldbookEntryId, table.chunkId] }),
  }),
);

// Zod schemas
export const insertWorldbookChunksSchema = createInsertSchema(worldbookChunks);

// 类型推导
export type NewWorldbookChunk = typeof worldbookChunks.$inferInsert;
export type WorldbookChunkItem = typeof worldbookChunks.$inferSelect;

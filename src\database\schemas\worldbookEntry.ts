/* eslint-disable sort-keys-fix/sort-keys-fix, typescript-sort-keys/interface */
import {
  boolean,
  integer,
  jsonb,
  pgTable,
  serial,
  text,
} from 'drizzle-orm/pg-core';
import { createInsertSchema } from 'drizzle-zod';

import { createdAt, updatedAt } from './_helpers';
import { users } from './user';
import { worldbooks } from './worldbook';

/**
 * worldbook_entries表
 */
export const worldbookEntries = pgTable('worldbook_entries', {
  // 基础标识字段
  id: serial('id').primaryKey(),
  worldbookId: text('worldbook_id')
    .references(() => worldbooks.id, { onDelete: 'cascade' })
    .notNull(),
  userId: text('user_id')
    .references(() => users.id, { onDelete: 'cascade' })
    .notNull(),
  clientId: text('client_id'),

  // 内容字段
  title: text('title'),
  content: text('content').notNull(),
  enabled: boolean('enabled').default(true).notNull(),

  // 排序字段
  orderIndex: integer('order_index').default(100).notNull(),
  displayIndex: integer('display_index').default(0).notNull(),

  // 激活规则字段
  keys: jsonb('keys').$type<string[]>().default([]).notNull(),
  keysSecondary: jsonb('keys_secondary').$type<string[]>().default([]).notNull(),
  selectiveLogic: text('selective_logic').default('and_any').notNull(),
  activationMode: text('activation_mode').default('keyword').notNull(),

  // 位置控制字段
  position: text('position').default('after').notNull(),
  depth: integer('depth').default(4).notNull(),
  role: text('role').default('system').notNull(),

  // 匹配配置字段
  matchSources: jsonb('match_sources').$type<Record<string, boolean>>().default({}).notNull(),
  caseSensitive: boolean('case_sensitive').default(false).notNull(),
  matchWholeWords: boolean('match_whole_words').default(false).notNull(),
  useRegex: boolean('use_regex').default(false).notNull(),

  // 概率控制字段
  probability: integer('probability').default(100).notNull(),
  useProbability: boolean('use_probability').default(true).notNull(),

  // 时间效果字段
  sticky: integer('sticky'),
  cooldown: integer('cooldown'),
  delay: integer('delay'),

  // 递归控制字段
  excludeRecursion: boolean('exclude_recursion').default(false).notNull(),
  preventRecursion: boolean('prevent_recursion').default(false).notNull(),
  delayUntilRecursion: integer('delay_until_recursion').default(0).notNull(),

  // 分组功能字段
  groupName: text('group_name'),
  groupWeight: integer('group_weight').default(100).notNull(),
  groupOverride: boolean('group_override').default(false).notNull(),
  useGroupScoring: boolean('use_group_scoring').default(false).notNull(),

  // 扫描控制字段
  scanDepth: integer('scan_depth'),

  // 显示控制字段
  addMemo: boolean('add_memo').default(false).notNull(),

  // 装饰器字段
  decorators: jsonb('decorators').$type<string[]>().default([]).notNull(),

  // 角色过滤字段
  characterFilterNames: jsonb('character_filter_names').$type<string[]>().default([]).notNull(),
  characterFilterTags: jsonb('character_filter_tags').$type<string[]>().default([]).notNull(),
  characterFilterExclude: boolean('character_filter_exclude').default(false).notNull(),

  // 时间戳字段
  createdAt: createdAt(),
  updatedAt: updatedAt(),
});

// Zod schemas
export const insertWorldbookEntriesSchema = createInsertSchema(worldbookEntries);

// 类型推导
export type NewWorldbookEntry = typeof worldbookEntries.$inferInsert;
export type WorldbookEntry = typeof worldbookEntries.$inferSelect;

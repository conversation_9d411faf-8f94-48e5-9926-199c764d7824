import type {
  CreateWorldbookParams,
  ImportResultDetail,
  UpdateWorldbookParams,
  ImportWorldbookParams,
  Worldbook,
} from '@/types/worldbook';
import type { WorldbookItem } from '@/database/schemas/worldbook';
import type {
  CreateWorldbookEntryParams,
  PaginatedResponse,
  SearchParams,
  UpdateWorldbookEntryParams,
} from '@/types/worldbook/entry';
import { WorldbookEntry } from '@/database/schemas';

/* eslint-disable typescript-sort-keys/interface */
export interface IWorldbookService {
  // ====== Worldbook Operations ======
  getWorldbooks(): Promise<Worldbook[]>;
  getWorldbook(id: string): Promise<WorldbookItem | null>;
  createWorldbook(data: CreateWorldbookParams): Promise<WorldbookItem>;
  updateWorldbook(id: string, data: UpdateWorldbookParams): Promise<WorldbookItem | null>;
  deleteWorldbook(id: string): Promise<void>;
  importWorldbook(data: ImportWorldbookParams): Promise<ImportResultDetail>;

  // ====== Entry Operations ======
  getEntry(id: number): Promise<WorldbookEntry | null>;
  searchEntries(worldbookId: string, params?: SearchParams): Promise<PaginatedResponse<WorldbookEntry>>;
  createEntry(worldbookId: string, data: CreateWorldbookEntryParams): Promise<WorldbookEntry>;
  updateEntry(id: number, data: UpdateWorldbookEntryParams): Promise<WorldbookEntry | null>;
  deleteEntry(id: number): Promise<void>;

  bulkDeleteEntries(ids: number[]): Promise<number>;
  bulkToggleEntries(ids: number[], enabled: boolean): Promise<number>;
}


